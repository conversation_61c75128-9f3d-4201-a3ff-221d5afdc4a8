const express = require('express');
const router = express.Router();

const login = async (req, res) => {
  try {
    const { email, password } = req.body;

    // 驗證輸入
    if (!email || !password) {
      return res.status(400).json({
        success: false,
        message: '請提供電子郵件和密碼'
      });
    }

    // 查找用戶
    const user = await User.findOne({ email }).select('+password');
    
    if (!user) {
      return res.status(401).json({
        success: false,
        message: '電子郵件或密碼錯誤'
      });
    }

    // 檢查帳戶是否活躍
    if (!user.isActive) {
      return res.status(401).json({
        success: false,
        message: '帳戶已被停用'
      });
    }

    // 驗證密碼
    const isPasswordValid = await user.comparePassword(password);
    
    if (!isPasswordValid) {
      return res.status(401).json({
        success: false,
        message: '電子郵件或密碼錯誤'
      });
    }

    // 更新最後登入時間
    user.lastLogin = new Date();
    await user.save();

    // 生成JWT令牌
    const token = generateToken(user._id); //MongoDB 自動生成的 _id

    res.json({
      success: true,
      message: '登入成功',
      data: {
        user: user.toJSON(),
        token
      }
    });
  } catch (error) {
    console.error('Login error:', error);
    res.status(500).json({
      success: false,
      message: '登入失敗',
      error: error.message
    });
  }
};